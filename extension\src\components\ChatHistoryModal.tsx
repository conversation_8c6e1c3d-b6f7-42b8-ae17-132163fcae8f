import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from './ui/dialog'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { ScrollArea } from './ui/scroll-area'
import { Search, MessageSquare, Plus, X } from 'lucide-react'
import type { Conversation } from '../types'

interface ChatHistoryModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  conversations: Conversation[]
  activeConversationId: string | null
  onSelectConversation: (conversationId: string) => void
  onNewChat: () => void
}

export function ChatHistoryModal({
  open,
  onOpenChange,
  conversations,
  activeConversationId,
  onSelectConversation,
  onNewChat
}: ChatHistoryModalProps) {
  const [searchQuery, setSearchQuery] = React.useState('')

  // Reset search when modal opens
  React.useEffect(() => {
    if (open) {
      setSearchQuery('')
    }
  }, [open])

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (open && e.key === 'Escape') {
        onOpenChange(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [open, onOpenChange])

  const filteredConversations = React.useMemo(() => {
    if (!searchQuery.trim()) return conversations
    const query = searchQuery.toLowerCase()
    return conversations.filter(conv => 
      conv.title?.toLowerCase().includes(query) ||
      conv.id.toLowerCase().includes(query)
    )
  }, [conversations, searchQuery])

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else if (diffDays < 30) {
      return `${Math.floor(diffDays / 7)} weeks ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const groupConversationsByDate = (conversations: Conversation[]) => {
    const groups: { [key: string]: Conversation[] } = {}
    
    conversations.forEach(conv => {
      const date = formatDate(conv.updated_at)
      if (!groups[date]) groups[date] = []
      groups[date].push(conv)
    })
    
    return groups
  }

  const conversationGroups = groupConversationsByDate(filteredConversations)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md h-[600px] p-0 flex flex-col">
        <DialogHeader className="p-4 pb-2 flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare size={20} />
              Chat history
              <span className="text-sm text-gray-500">({conversations.length})</span>
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-6 w-6 p-0"
            >
              <X size={16} />
            </Button>
          </div>
        </DialogHeader>

        <div className="px-4 pb-2 flex-shrink-0">
          <Button
            onClick={() => {
              onNewChat()
              onOpenChange(false)
            }}
            className="w-full mb-3 bg-black text-white hover:bg-gray-800"
            size="sm"
          >
            <Plus size={16} className="mr-2" />
            New chat
            <span className="text-xs text-gray-400 ml-auto">Ctrl+N</span>
          </Button>

          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-9"
            />
          </div>
        </div>

        <ScrollArea className="flex-1 px-4">
          <div className="space-y-4 pb-4">
            {Object.entries(conversationGroups).map(([dateGroup, convs]) => (
              <div key={dateGroup}>
                <div className="text-xs font-medium text-gray-500 mb-2 sticky top-0 bg-white py-1">
                  {dateGroup}
                </div>
                <div className="space-y-1">
                  {convs.map((conversation) => {
                    const isActive = conversation.id === activeConversationId
                    const displayTitle = conversation.title || 'New chat'
                    
                    return (
                      <button
                        key={conversation.id}
                        onClick={() => {
                          onSelectConversation(conversation.id)
                          onOpenChange(false)
                        }}
                        className={`w-full text-left p-3 rounded-lg border transition-colors ${
                          isActive 
                            ? 'bg-blue-50 border-blue-200 text-blue-900' 
                            : 'bg-white border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <div className="font-medium text-sm truncate mb-1">
                          {displayTitle}
                        </div>
                        <div className="text-xs text-gray-500 truncate">
                          {new Date(conversation.updated_at).toLocaleString()}
                        </div>
                      </button>
                    )
                  })}
                </div>
              </div>
            ))}
            
            {filteredConversations.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                <MessageSquare size={48} className="mx-auto mb-4 text-gray-300" />
                <div className="text-sm">
                  {searchQuery ? 'No conversations found' : 'No chat history yet'}
                </div>
                <div className="text-xs mt-1">
                  {searchQuery ? 'Try a different search term' : 'Start a conversation to see it here'}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
