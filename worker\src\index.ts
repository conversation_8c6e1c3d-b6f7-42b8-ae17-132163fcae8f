import { Hono } from 'hono'
import { cors } from 'hono/cors'
import type { D1Database } from 'cloudflare:workers'

type Env = {
  GOOGLE_CLIENT_ID: string
  GOOGLE_CLIENT_SECRET: string
  AI_GATEWAY_URL?: string
  AI_GATEWAY_TOKEN?: string
  GOOGLE_AI_STUDIO_API_KEY?: string
  SESSION_SECRET: string
  ENCRYPTION_KEY: string // base64 or hex (32 bytes for AES-256)
  TOKEN_VAULT: DurableObjectNamespace
  GmailMCP: DurableObjectNamespace
  // Workers AI binding (optional). Using any to avoid requiring extra types.
  AI?: any
  // D1 binding (optional)
  DB?: D1Database
}

// --- Utils ---
import { encrypt, decrypt } from './utils/crypto'
import { TokenVault, vaultPut, vaultGet } from './utils/vault'
import { GmailMCP } from './mcp/gmail'
import { ensureSchema, listConversations, listMessages, insertUserMessage, insertMessage, upsertConversation } from './utils/d1'

// Re-export DO class for Wrangler binding
export { TokenVault }
export { GmailMCP } from './mcp/gmail'

// --- App ---
const app = new Hono<{ Bindings: Env }>()

// TODO: tighten CORS to the extension origin (chrome-extension://<id>) and production domain
app.use('*', cors({ origin: '*', allowHeaders: ['*'], allowMethods: ['GET', 'POST', 'OPTIONS'] }))

app.get('/health', (c) => c.json({ ok: true, service: 'ai-email-agent-worker' }))

app.get('/oauth/google/config', (c) => {
  const client_id = c.env.GOOGLE_CLIENT_ID
  return c.json({ client_id })
})

// --- Phase 5: Conversations & Chat Streaming ---
// List conversations (most recent first)
app.get('/conversations', async (c) => {
  if (!c.env.DB) return c.json({ error: 'DB not configured' }, 500)
  await ensureSchema(c.env.DB)
  const limit = Number(new URL(c.req.url).searchParams.get('limit') || '100')
  const rows = await listConversations(c.env.DB, Number.isFinite(limit) && limit > 0 ? limit : 100)
  return c.json({ conversations: rows })
})

// Get messages for a conversation
app.get('/conversations/:id/messages', async (c) => {
  if (!c.env.DB) return c.json({ error: 'DB not configured' }, 500)
  await ensureSchema(c.env.DB)
  const id = c.req.param('id')
  const limit = Number(new URL(c.req.url).searchParams.get('limit') || '500')
  const rows = await listMessages(c.env.DB, id, Number.isFinite(limit) && limit > 0 ? limit : 500)
  return c.json({ messages: rows })
})

// Simple helper to stream text chunks as SSE to the client
function sseStream(init?: ResponseInit) {
  let controller: ReadableStreamDefaultController<Uint8Array>
  const encoder = new TextEncoder()
  const stream = new ReadableStream<Uint8Array>({
    start(c) {
      controller = c
    },
  })
  const write = (data: string) => {
    controller.enqueue(encoder.encode(`data: ${data}\n\n`))
  }
  const close = () => controller.close()
  const response = new Response(stream, {
    headers: {
      'content-type': 'text/event-stream; charset=utf-8',
      'cache-control': 'no-cache, no-transform',
      'x-accel-buffering': 'no',
      ...(init?.headers || {}),
    },
    status: init?.status ?? 200,
  })
  return { response, write, close }
}

// POST /chat/ask => SSE stream of assistant response, persists user+assistant messages
// Body: { conversationId?: string, message: string, title?: string, accountId?: string }
app.post('/chat/ask', async (c) => {
  if (!c.env.DB) return c.json({ error: 'DB not configured' }, 500)
  await ensureSchema(c.env.DB)
  type Body = { conversationId?: string, message: string, title?: string | null, accountId?: string | null, gmailAccessToken?: string | null }
  const body = await c.req.json<Body>().catch(() => null)
  if (!body || !body.message) return c.json({ error: 'invalid_request' }, 400)

  const conversationId = body.conversationId || crypto.randomUUID()
  const title = body.title ?? null
  const accountId = body.accountId ?? null

  // Upsert conversation and insert user message
  await upsertConversation(c.env.DB, conversationId, title, accountId)
  await insertUserMessage(c.env.DB, conversationId, body.message)

  // Prepare SSE
  const { response, write, close } = sseStream()
  // Emit metadata including conversationId so client can attach UI state
  write(JSON.stringify({ type: 'meta', conversationId }))

  // Kick off AI Gateway request (Universal Endpoint)
  const gateway = c.env.AI_GATEWAY_URL
  if (!gateway) {
    // Send a single chunk error and close
    queueMicrotask(() => {
      write(JSON.stringify({ type: 'error', message: 'AI Gateway not configured' }))
      close()
    })
    return response
  }

  const providerHeaders: Record<string, string> = {}
  if (c.env.GOOGLE_AI_STUDIO_API_KEY) {
    providerHeaders['Authorization'] = `Bearer ${c.env.GOOGLE_AI_STUDIO_API_KEY}`
    providerHeaders['x-goog-api-key'] = c.env.GOOGLE_AI_STUDIO_API_KEY
  }

  // Optional: fetch recent Gmail messages as context
  let emailContext = ''
  if (body.gmailAccessToken) {
    try {
      const q = 'in:inbox newer_than:7d'
      const listUrl = new URL('https://gmail.googleapis.com/gmail/v1/users/me/messages')
      listUrl.searchParams.set('maxResults', '10')
      listUrl.searchParams.set('q', q)
      const listRes = await fetch(listUrl.toString(), { headers: { Authorization: `Bearer ${body.gmailAccessToken}` } })
      if (listRes.ok) {
        const lj = await listRes.json<any>()
        const ids: string[] = (lj.messages?.map((m: any) => m.id) || []).slice(0, 10)
        const parts: string[] = []
        for (const id of ids) {
          try {
            const msgRes = await fetch(`https://gmail.googleapis.com/gmail/v1/users/me/messages/${id}?format=full`, { headers: { Authorization: `Bearer ${body.gmailAccessToken}` } })
            if (!msgRes.ok) continue
            const mj = await msgRes.json<any>()
            // Extract subject and snippet/plain text
            const headers = (mj.payload?.headers || []) as Array<{ name: string, value: string }>
            const subject = headers.find(h => h.name?.toLowerCase() === 'subject')?.value || '(no subject)'
            let plain = ''
            const stack = [mj.payload]
            while (stack.length) {
              const p = stack.pop()
              if (!p) continue
              if (p.mimeType === 'text/plain' && p.body?.data) {
                const b64 = String(p.body.data).replace(/-/g, '+').replace(/_/g, '/')
                plain += atob(b64)
              }
              if (p.parts) for (const child of p.parts) stack.push(child)
            }
            if (!plain && mj.snippet) plain = mj.snippet
            if (plain) parts.push(`- Subject: ${subject}\n${plain.slice(0, 2000)}`)
          } catch {}
        }
        if (parts.length) {
          emailContext = parts.join('\n\n---\n\n').slice(0, 12000)
          // Persist a tool trace entry so UI shows we fetched inbox
          try {
            await insertMessage(c.env.DB!, {
              conversation_id: conversationId,
              role: 'tool',
              tool_name: 'gmail.fetchInbox',
              args_json: JSON.stringify({ q, maxResults: 10 }),
              content: null,
              result_excerpt: JSON.stringify({ messagesFetched: parts.length }).slice(0, 1000),
            })
          } catch {}
        }
      }
    } catch {}
  }

  const payload = [
    {
      provider: 'compat',
      endpoint: 'chat/completions',
      headers: providerHeaders,
      query: {
        model: 'google-ai-studio/gemini-2.5-flash-lite',
        messages: [
          { role: 'system', content: 'You are an expert email assistant. Keep responses concise unless asked otherwise.' },
          ...(emailContext ? [{ role: 'system', content: `Recent inbox context (last 7 days, up to 10 emails). Use this to answer user requests.\n\n${emailContext}` }] as any : []),
          { role: 'user', content: body.message },
        ],
      },
    },
  ]

  ;(async () => {
    let full = ''
    try {
      const resp = await fetch(gateway, {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'accept': 'text/event-stream, application/json',
          ...(c.env.AI_GATEWAY_TOKEN ? { 'cf-aig-authorization': `Bearer ${c.env.AI_GATEWAY_TOKEN}` } : {}),
        },
        body: JSON.stringify(payload),
      })
      if (!resp.ok) {
        const detail = await resp.text()
        write(JSON.stringify({ type: 'error', message: `AI Gateway error ${resp.status}: ${detail}` }))
        return
      }
      const ct = resp.headers.get('content-type') || ''
      if (ct.includes('text/event-stream') && resp.body) {
        const reader = resp.body.getReader()
        const decoder = new TextDecoder()
        let buffer = ''
        while (true) {
          const { value, done } = await reader.read()
          if (done) break
          buffer += decoder.decode(value, { stream: true })
          const events = buffer.split('\n\n')
          buffer = events.pop() || ''
          for (const evt of events) {
            for (const line of evt.split('\n')) {
              const trimmed = line.trim()
              if (!trimmed.startsWith('data:')) continue
              const jsonStr = trimmed.slice(5).trim()
              if (!jsonStr) continue
              try {
                const obj = JSON.parse(jsonStr)
                if (obj?.type === 'universal.stream' && obj?.response?.response) {
                  const delta = String(obj.response.response)
                  full += delta
                  write(JSON.stringify({ type: 'delta', text: delta }))
                }
              } catch {}
            }
          }
        }
      } else {
        // JSON fallback
        const j = await resp.json<any>()
        const text = j?.[0]?.response?.result?.response
          ?? j?.choices?.[0]?.message?.content
          ?? JSON.stringify(j).slice(0, 4000)
        full = String(text)
        write(JSON.stringify({ type: 'delta', text: full }))
      }
    } catch (e: any) {
      write(JSON.stringify({ type: 'error', message: String(e?.message || e) }))
    } finally {
      try {
        // Persist assistant message
        await insertMessage(c.env.DB!, {
          conversation_id: conversationId,
          role: 'assistant',
          content: full,
          result_excerpt: full ? full.slice(0, 1000) : null,
        })
      } catch {}
      write(JSON.stringify({ type: 'done' }))
      close()
    }
  })()

  return response
})

app.post('/oauth/google/token', async (c) => {
  const { code, code_verifier, redirect_uri } = await c.req.json<{
    code: string
    code_verifier: string
    redirect_uri: string
  }>()
  if (!code || !code_verifier || !redirect_uri) return c.json({ error: 'invalid_request' }, 400)

  const body = new URLSearchParams({
    code,
    code_verifier,
    redirect_uri,
    client_id: c.env.GOOGLE_CLIENT_ID,
    client_secret: c.env.GOOGLE_CLIENT_SECRET,
    grant_type: 'authorization_code',
  })
  const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    body,
  })
  if (!tokenRes.ok) {
    const err = await tokenRes.text()
    return c.json({ error: 'exchange_failed', detail: err }, 502)
  }
  const tokenJson = await tokenRes.json<any>()
  const access_token = tokenJson.access_token as string
  const refresh_token = tokenJson.refresh_token as string | undefined
  const expires_in = tokenJson.expires_in as number
  const id_token = tokenJson.id_token as string | undefined

  // Fetch userinfo (safer than decoding id_token across issuers)
  let user: any = undefined
  try {
    const u = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
      headers: { Authorization: `Bearer ${access_token}` },
    })
    if (u.ok) user = await u.json<any>()
  } catch {}

  let tokensRef = ''
  if (refresh_token) {
    tokensRef = crypto.randomUUID()
    const enc = await encrypt(c.env, refresh_token)
    await vaultPut(c.env.TOKEN_VAULT, tokensRef, enc)
  }

  return c.json({ access_token, expires_in, refresh_token: tokensRef, id_token, user })
})

app.post('/oauth/google/refresh', async (c) => {
  const { refresh_token } = await c.req.json<{ refresh_token: string }>()
  if (!refresh_token) return c.json({ error: 'invalid_request' }, 400)
  const enc = await vaultGet(c.env.TOKEN_VAULT, refresh_token)
  if (!enc) return c.json({ error: 'not_found' }, 404)
  const realRefresh = await decrypt(c.env, enc)

  const body = new URLSearchParams({
    client_id: c.env.GOOGLE_CLIENT_ID,
    client_secret: c.env.GOOGLE_CLIENT_SECRET,
    grant_type: 'refresh_token',
    refresh_token: realRefresh,
  })
  const r = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    body,
  })
  if (!r.ok) {
    const detail = await r.text()
    return c.json({ error: 'refresh_failed', detail }, 502)
  }
  const j = await r.json<any>()
  return c.json({ access_token: j.access_token, expires_in: j.expires_in })
})

// --- MCP Server handler ---
const mcpHandler = GmailMCP.serve('/mcp', { binding: 'GmailMCP' }) as unknown as {
  fetch: (request: Request, env: Env, ctx: ExecutionContext) => Promise<Response>
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext) {
    const url = new URL(request.url)
    if (url.pathname.startsWith('/mcp')) {
      return mcpHandler.fetch(request, env, ctx)
    }
    return app.fetch(request, env, ctx)
  },
}
